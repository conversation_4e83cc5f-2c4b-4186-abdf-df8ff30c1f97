import React, { useState } from 'react';
import GoJSDecisionProcessGraph from './GoJSDecisionProcessGraph';
import { Section } from '../ui/Section';
import { Panel, PanelContent, PanelTitle } from '../ui/Panel';
import { Col, Row } from '../../ui/Grid';

const DecisionProcessGraphPage: React.FC = () => {
  const [selectedProcess, setSelectedProcess] = useState<string>('');
  const [graphType, setGraphType] = useState<'gojs' | 'reactflow' | 'd3'>('gojs');
  const [availableProcesses] = useState([
    'document-element-accounting-accounts-strategy-selection',
    'access-lump-sum-accounting-accounts-strategy-selection',
    'travel-expense-accounting-accounts-strategy-selection',
    'technical-accounting-accounts-strategy-selection',
    'provider-accounting-accounts-strategy-selection',
    'mileage-allowance-accounting-accounts-strategy-selection',
    'drive-lump-sum-accounting-accounts-strategy-selection',
    'accommodation-lum-sum-accounting-accounts-strategy-selection',
    'foreign-travel-allowance-estimation-strategy-selection',
    'foreign-travel-allowance-strategy-selection',
    'document-exchange-rate-strategy-selection',
    'settlement-acceptor-strategy-selection',
    'request-acceptor-strategy-selection',
  ]);

  return (
    <Section>
      <Row>
        <Col xs={12}>
          <Panel>
            <PanelTitle>
              <h2>Decision Process Flow Visualization</h2>
            </PanelTitle>
            <PanelContent>
              <div style={{ marginBottom: '1rem' }}>
                <label style={{ marginRight: '1rem' }}>
                  <input
                    type='radio'
                    checked={graphType === 'gojs'}
                    onChange={() => setGraphType('gojs')}
                    style={{ marginRight: '0.5rem' }}
                  />
                  GoJS (New)
                </label>
                <label style={{ marginRight: '1rem' }}>
                  <input
                    type='radio'
                    checked={graphType === 'reactflow'}
                    onChange={() => setGraphType('reactflow')}
                    style={{ marginRight: '0.5rem' }}
                  />
                  React Flow
                </label>
                <label>
                  <input
                    type='radio'
                    checked={graphType === 'd3'}
                    onChange={() => setGraphType('d3')}
                    style={{ marginRight: '0.5rem' }}
                  />
                  D3.js (Legacy)
                </label>
              </div>

              <div className='controls' style={{ marginBottom: '1rem' }}>
                <label htmlFor='process-select' style={{ marginRight: '0.5rem' }}>
                  Select Process:
                </label>
                <select
                  id='process-select'
                  value={selectedProcess}
                  onChange={(e) => setSelectedProcess(e.target.value)}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '4px',
                    border: '1px solid #ddd',
                    marginRight: '1rem',
                  }}
                >
                  <option value=''>All Processes</option>
                  {availableProcesses.map((process) => (
                    <option key={process} value={process}>
                      {process.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>

                <button
                  onClick={() => setSelectedProcess('')}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  Show All
                </button>
              </div>

              <div
                className='graph-info'
                style={{
                  marginBottom: '1rem',
                  padding: '1rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                }}
              >
                <h4>How to use this graph:</h4>
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1.5rem' }}>
                  <li>
                    <strong>Nodes</strong> represent decision process nodes with their decision
                    codes
                  </li>
                  <li>
                    <strong>Colored circles</strong> group nodes by process
                  </li>
                  <li>
                    <strong>Small circles</strong> on nodes show condition count (red = has
                    conditions, green = no conditions)
                  </li>
                  <li>
                    <strong>Arrows</strong> show the flow between nodes (next if conditions fail)
                  </li>
                  <li>
                    <strong>Click nodes</strong> to see detailed information including conditions
                  </li>
                  <li>
                    <strong>Drag nodes</strong> to rearrange the layout
                  </li>
                  <li>
                    <strong>Zoom and pan</strong> to navigate large graphs
                  </li>
                </ul>
              </div>
            </PanelContent>
          </Panel>
        </Col>
      </Row>

      <Row>
        <Col xs={12}>
          {graphType === 'gojs' && (
            <GoJSDecisionProcessGraph
              processSlug={selectedProcess || undefined}
              width={1200}
              height={800}
            />
          )}
          {graphType === 'reactflow' && (
            <ReactFlowGraph
              processSlug={selectedProcess || undefined}
              width={1200}
              height={800}
            />
          )}
          {graphType === 'd3' && (
            <DecisionProcessGraph
              processSlug={selectedProcess || undefined}
              width={1200}
              height={800}
            />
          )}
        </Col>
      </Row>
    </Section>
  );
};

export default DecisionProcessGraphPage;
